import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Users, 
  ClipboardList, 
  CheckCircle, 
  DollarSign, 
  UserPlus, 
  PlusCircle, 
  BarChart3, 
  Settings,
  TrendingUp,
  TrendingDown
} from "lucide-react";

const stats = [
  {
    title: "Total Clients",
    value: "1,247",
    change: "+12%",
    trend: "up",
    icon: Users,
    color: "blue",
  },
  {
    title: "Active Requests",
    value: "89",
    change: "-3%",
    trend: "down",
    icon: ClipboardList,
    color: "amber",
  },
  {
    title: "Approved Requests",
    value: "24",
    change: "+8%",
    trend: "up",
    icon: CheckCircle,
    color: "green",
  },
  {
    title: "Finance Given",
    value: "KWD 54,290",
    change: "+15%",
    trend: "up",
    icon: DollarSign,
    color: "purple",
  },
];

const recentActivities = [
  {
    type: "user-plus",
    title: "New client registration",
    time: "2 minutes ago",
    color: "blue",
  },
  {
    type: "check",
    title: "Request #1234 completed",
    time: "5 minutes ago",
    color: "green",
  },
  {
    type: "exclamation",
    title: "High priority request assigned",
    time: "10 minutes ago",
    color: "amber",
  },
];

const quickActions = [
  {
    title: "Add Client",
    icon: UserPlus,
    color: "blue",
  },
  {
    title: "New Request",
    icon: PlusCircle,
    color: "green",
  },
  {
    title: "Reports",
    icon: BarChart3,
    color: "purple",
  },
  {
    title: "Settings",
    icon: Settings,
    color: "gray",
  },
];

const getColorClasses = (color: string, type: "bg" | "text" | "icon") => {
  const colors = {
    blue: {
      bg: "bg-blue-100",
      text: "text-blue-600",
      icon: "text-blue-600",
    },
    amber: {
      bg: "bg-amber-100",
      text: "text-amber-600",
      icon: "text-amber-600",
    },
    green: {
      bg: "bg-green-100",
      text: "text-green-600",
      icon: "text-green-600",
    },
    purple: {
      bg: "bg-purple-100",
      text: "text-purple-600",
      icon: "text-purple-600",
    },
    gray: {
      bg: "bg-gray-100",
      text: "text-gray-600",
      icon: "text-gray-600",
    },
  };
  return colors[color as keyof typeof colors]?.[type] || colors.gray[type];
};

export default function Dashboard() {
  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard Overview</h1>
        <p className="text-gray-600">Welcome to your management dashboard</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.title} className="border border-gray-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <div className={`w-12 h-12 ${getColorClasses(stat.color, "bg")} rounded-lg flex items-center justify-center`}>
                    <Icon className={`${getColorClasses(stat.color, "icon")} w-6 h-6`} />
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  {stat.trend === "up" ? (
                    <TrendingUp className="text-green-600 w-4 h-4 mr-1" />
                  ) : (
                    <TrendingDown className="text-red-600 w-4 h-4 mr-1" />
                  )}
                  <span className={stat.trend === "up" ? "text-green-600" : "text-red-600"}>
                    {stat.change}
                  </span>
                  <span className="text-gray-600 text-sm ml-2">from last month</span>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Recent Activity and Quick Actions */}
      {/*
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="border border-gray-200">
          <CardHeader className="border-b border-gray-200">
            <CardTitle className="text-lg font-semibold text-gray-900">Recent Activity</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              {recentActivities.map((activity, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className={`w-8 h-8 ${getColorClasses(activity.color, "bg")} rounded-full flex items-center justify-center flex-shrink-0`}>
                    {activity.type === "user-plus" && <UserPlus className={`${getColorClasses(activity.color, "icon")} w-4 h-4`} />}
                    {activity.type === "check" && <CheckCircle className={`${getColorClasses(activity.color, "icon")} w-4 h-4`} />}
                    {activity.type === "exclamation" && <span className={`${getColorClasses(activity.color, "text")} text-sm font-bold`}>!</span>}
                  </div>
                  <div>
                    <p className="text-sm text-gray-900">{activity.title}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="border border-gray-200">
          <CardHeader className="border-b border-gray-200">
            <CardTitle className="text-lg font-semibold text-gray-900">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-2 gap-4">
              {quickActions.map((action, index) => {
                const Icon = action.icon;
                return (
                  <Button
                    key={index}
                    variant="outline"
                    className="h-auto flex flex-col items-center p-4 hover:bg-gray-50 border-gray-200"
                  >
                    <Icon className={`${getColorClasses(action.color, "text")} w-6 h-6 mb-2`} />
                    <span className="text-sm font-medium text-gray-900">{action.title}</span>
                  </Button>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>
       */}
    </div>

  );
}
